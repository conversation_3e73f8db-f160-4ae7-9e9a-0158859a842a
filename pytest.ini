[tool:pytest]
# Pytest configuration for autolodge_retrained_deploy

# Test discovery
testpaths = test
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=autolodge
    --cov=deploy
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=99

# Markers
markers =
    unit: Unit tests
    integration: Integration tests  
    azure: Azure integration tests
    slow: Slow running tests
    security: Security-related tests

# Filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:tensorflow.*

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment to enable parallel execution with pytest-xdist
