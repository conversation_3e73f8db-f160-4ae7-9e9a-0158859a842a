"""
Comprehensive unit tests for the log_utils module.

This module provides extensive test coverage for logging utilities including
Azure Blob Storage integration, configuration management, and fallback mechanisms.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

import pytest
from azure.storage.blob import BlobServiceClient

# Import the module under test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / 'autolodge'))

from log_utils import (
    AzureLoggingConfig,
    AzureBlobStorageSink,
    setup_logging,
    check_azure_dependencies
)
from log_utils.config import get_azure_logging_config
from log_utils.utils import _setup_local_file_logging, add_console_handler, add_file_handler


class TestAzureLoggingConfig:
    """Test cases for AzureLoggingConfig class."""
    
    @pytest.mark.unit
    def test_azure_logging_config_initialization(self, mock_environment_variables):
        """Test AzureLoggingConfig initialization."""
        config = AzureLoggingConfig()
        
        assert config.connection_string == '<your-storage-connection-string>'
        assert config.container_name == 'test-logs'
        assert config.blob_prefix == 'test'
        assert config.environment == 'TEST'
    
    @pytest.mark.unit
    def test_azure_logging_config_defaults(self):
        """Test AzureLoggingConfig default values."""
        with patch.dict(os.environ, {}, clear=True):
            config = AzureLoggingConfig()
            
            assert config.container_name == 'autolodge-logs'
            assert config.blob_prefix == 'score'
            assert config.environment == 'LOCAL'
    
    @pytest.mark.unit
    def test_azure_logging_config_environment_detection(self, mock_environment_variables):
        """Test environment detection in AzureLoggingConfig."""
        config = AzureLoggingConfig()
        
        # Test direct environment variable detection
        assert config.environment == 'TEST'
    
    @pytest.mark.unit
    def test_azure_logging_config_workspace_name_detection(self):
        """Test environment detection from workspace name."""
        env_vars = {
            'AZURE_ML_WORKSPACE_NAME': 'ps-prod-ml-ws-claimsauto'
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = AzureLoggingConfig()
            assert config.environment == 'PROD'
    
    @pytest.mark.unit
    def test_azure_logging_config_model_name_detection(self):
        """Test environment detection from model name."""
        env_vars = {
            'MODEL_NAME': 'ps-dev-ca-tstar'
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = AzureLoggingConfig()
            assert config.environment == 'DEV'
    
    @pytest.mark.unit
    def test_azure_logging_config_is_configured_true(self, mock_environment_variables):
        """Test is_configured method returns True when configured."""
        config = AzureLoggingConfig()
        config.connection_string = 'valid_connection_string'
        
        assert config.is_configured() is True
    
    @pytest.mark.unit
    def test_azure_logging_config_is_configured_false(self):
        """Test is_configured method returns False when not configured."""
        with patch.dict(os.environ, {}, clear=True):
            config = AzureLoggingConfig()
            
            assert config.is_configured() is False
    
    @pytest.mark.unit
    def test_azure_logging_config_get_connection_string_direct(self):
        """Test get_connection_string with direct connection string."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string'
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = AzureLoggingConfig()
            
            assert config.get_connection_string() == 'test_connection_string'
    
    @pytest.mark.unit
    def test_azure_logging_config_get_connection_string_components(self):
        """Test get_connection_string with account name and key."""
        env_vars = {
            'AZURE_STORAGE_ACCOUNT_NAME': 'testaccount',
            'AZURE_STORAGE_ACCOUNT_KEY': 'testkey'
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = AzureLoggingConfig()
            
            connection_string = config.get_connection_string()
            assert 'testaccount' in connection_string
            assert 'testkey' in connection_string
    
    @pytest.mark.unit
    def test_azure_logging_config_log_configuration_status(self, mock_environment_variables, caplog):
        """Test log_configuration_status method."""
        config = AzureLoggingConfig()
        config.connection_string = 'test_connection_string'
        
        with caplog.at_level("INFO"):
            config.log_configuration_status()
        
        assert "Azure Storage connection string" in caplog.text
        assert "Azure Blob Storage container" in caplog.text
    
    @pytest.mark.unit
    def test_azure_logging_config_log_configuration_hints(self, caplog):
        """Test log_configuration_hints method."""
        with patch.dict(os.environ, {}, clear=True):
            config = AzureLoggingConfig()
            
            with caplog.at_level("WARNING"):
                config.log_configuration_hints()
            
            assert "Azure Storage connection string not configured" in caplog.text


class TestAzureBlobStorageSink:
    """Test cases for AzureBlobStorageSink class."""
    
    @pytest.mark.unit
    @patch('log_utils.azure_sink.BlobServiceClient')
    def test_azure_blob_storage_sink_initialization(self, mock_blob_client, mock_environment_variables):
        """Test AzureBlobStorageSink initialization."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection',
            container_name='test-container',
            blob_prefix='test-prefix',
            environment='TEST'
        )
        
        assert sink.connection_string == 'test_connection'
        assert sink.container_name == 'test-container'
        assert sink.blob_prefix == 'test-prefix'
        assert sink.environment == 'TEST'
        assert sink.log_buffer == []
    
    @pytest.mark.unit
    @patch('log_utils.azure_sink.BlobServiceClient')
    def test_azure_blob_storage_sink_blob_client_initialization(self, mock_blob_client, mock_environment_variables):
        """Test blob client initialization."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection',
            container_name='test-container',
            blob_prefix='test-prefix',
            environment='TEST'
        )
        
        mock_blob_client.from_connection_string.assert_called_once_with('test_connection')
    
    @pytest.mark.unit
    @patch('log_utils.azure_sink.BlobServiceClient')
    def test_azure_blob_storage_sink_write_message(self, mock_blob_client, mock_environment_variables):
        """Test writing message to sink."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection',
            container_name='test-container',
            blob_prefix='test-prefix',
            environment='TEST'
        )
        
        # Mock message record
        mock_message = Mock()
        mock_message.record = {
            'time': Mock(),
            'level': Mock(),
            'name': 'test',
            'function': 'test_func',
            'line': 123,
            'message': 'test message'
        }
        mock_message.record['time'].strftime = Mock(return_value='2023-01-01 12:00:00')
        mock_message.record['level'].__str__ = Mock(return_value='INFO')
        
        sink.write(mock_message)
        
        assert len(sink.log_buffer) == 1
    
    @pytest.mark.unit
    @patch('log_utils.azure_sink.BlobServiceClient')
    def test_azure_blob_storage_sink_buffer_upload_trigger(self, mock_blob_client, mock_environment_variables):
        """Test buffer upload trigger when buffer is full."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection',
            container_name='test-container',
            blob_prefix='test-prefix',
            environment='TEST'
        )
        sink.max_buffer_size = 2  # Set small buffer size for testing
        
        # Mock message record
        mock_message = Mock()
        mock_message.record = {
            'time': Mock(),
            'level': Mock(),
            'name': 'test',
            'function': 'test_func',
            'line': 123,
            'message': 'test message'
        }
        mock_message.record['time'].strftime = Mock(return_value='2023-01-01 12:00:00')
        mock_message.record['level'].__str__ = Mock(return_value='INFO')
        
        with patch.object(sink, '_upload_buffer') as mock_upload:
            # Fill buffer to trigger upload
            sink.write(mock_message)
            sink.write(mock_message)
            
            mock_upload.assert_called_once()


class TestSetupLogging:
    """Test cases for setup_logging function."""
    
    @pytest.mark.unit
    @patch('log_utils.utils.check_azure_dependencies', return_value=True)
    @patch('log_utils.utils.AzureBlobStorageSink')
    def test_setup_logging_azure_success(self, mock_sink_class, mock_check_deps, mock_environment_variables):
        """Test setup_logging with successful Azure configuration."""
        mock_sink = Mock()
        mock_sink.current_blob_name = 'test-blob.log'
        mock_sink_class.return_value = mock_sink
        
        with patch('log_utils.utils.AzureLoggingConfig') as mock_config_class:
            mock_config = Mock()
            mock_config.is_configured.return_value = True
            mock_config.get_connection_string.return_value = 'test_connection'
            mock_config.container_name = 'test-container'
            mock_config.blob_prefix = 'test-prefix'
            mock_config.environment = 'TEST'
            mock_config_class.return_value = mock_config
            
            result = setup_logging()
            
            assert 'azure://' in result
            mock_sink_class.assert_called_once()
    
    @pytest.mark.unit
    @patch('log_utils.utils.check_azure_dependencies', return_value=False)
    def test_setup_logging_azure_unavailable(self, mock_check_deps, mock_environment_variables):
        """Test setup_logging when Azure dependencies are unavailable."""
        with patch('log_utils.utils._setup_local_file_logging') as mock_local_setup:
            mock_local_setup.return_value = '/path/to/local.log'
            
            result = setup_logging()
            
            mock_local_setup.assert_called_once()
            assert result == '/path/to/local.log'
    
    @pytest.mark.unit
    @patch('log_utils.utils.check_azure_dependencies', return_value=True)
    def test_setup_logging_azure_not_configured(self, mock_check_deps, mock_environment_variables):
        """Test setup_logging when Azure is available but not configured."""
        with patch('log_utils.utils.AzureLoggingConfig') as mock_config_class, \
             patch('log_utils.utils._setup_local_file_logging') as mock_local_setup:
            
            mock_config = Mock()
            mock_config.is_configured.return_value = False
            mock_config_class.return_value = mock_config
            mock_local_setup.return_value = '/path/to/local.log'
            
            result = setup_logging()
            
            mock_local_setup.assert_called_once()
            assert result == '/path/to/local.log'
    
    @pytest.mark.unit
    @patch('log_utils.utils.check_azure_dependencies', return_value=True)
    @patch('log_utils.utils.AzureBlobStorageSink', side_effect=Exception("Azure setup failed"))
    def test_setup_logging_azure_failure_fallback(self, mock_sink_class, mock_check_deps, mock_environment_variables):
        """Test setup_logging fallback when Azure setup fails."""
        with patch('log_utils.utils.AzureLoggingConfig') as mock_config_class, \
             patch('log_utils.utils._setup_local_file_logging') as mock_local_setup:
            
            mock_config = Mock()
            mock_config.is_configured.return_value = True
            mock_config.get_connection_string.return_value = 'test_connection'
            mock_config.container_name = 'test-container'
            mock_config.blob_prefix = 'test-prefix'
            mock_config.environment = 'TEST'
            mock_config_class.return_value = mock_config
            mock_local_setup.return_value = '/path/to/local.log'
            
            result = setup_logging()
            
            mock_local_setup.assert_called_once()
            assert result == '/path/to/local.log'


class TestLocalFileLogging:
    """Test cases for local file logging functions."""
    
    @pytest.mark.unit
    def test_setup_local_file_logging(self, mock_environment_variables, temp_dir):
        """Test _setup_local_file_logging function."""
        mock_config = Mock()
        mock_config.environment = 'TEST'
        
        with patch('log_utils.utils.Path') as mock_path_class:
            # Mock repository root path
            mock_path = Mock()
            mock_path.parent.parent.parent = temp_dir
            mock_path_class.return_value = mock_path
            mock_path_class.__file__ = str(temp_dir / 'log_utils' / 'utils.py')
            
            # Mock logs directory
            logs_dir = temp_dir / 'logs'
            logs_dir.mkdir(exist_ok=True)
            mock_logs_dir = Mock()
            mock_logs_dir.mkdir = Mock()
            mock_logs_dir.__truediv__ = Mock(return_value=temp_dir / 'logs' / 'test.log')
            
            with patch('log_utils.utils.logger.add') as mock_add:
                result = _setup_local_file_logging(True, mock_config)
                
                mock_add.assert_called_once()
                assert isinstance(result, str)
    
    @pytest.mark.unit
    def test_add_console_handler(self):
        """Test add_console_handler function."""
        with patch('log_utils.utils.logger.add') as mock_add:
            add_console_handler()
            
            mock_add.assert_called_once()
            # Verify console handler configuration
            call_args = mock_add.call_args
            assert 'colorize' in call_args.kwargs
            assert call_args.kwargs['colorize'] is True
    
    @pytest.mark.unit
    def test_add_file_handler(self, temp_dir):
        """Test add_file_handler function."""
        log_file = temp_dir / 'test.log'
        
        with patch('log_utils.utils.logger.add') as mock_add:
            add_file_handler(log_file, level='INFO')
            
            mock_add.assert_called_once()
            call_args = mock_add.call_args
            assert call_args.kwargs['level'] == 'INFO'
            assert 'rotation' in call_args.kwargs


class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    @pytest.mark.unit
    def test_check_azure_dependencies_available(self):
        """Test check_azure_dependencies when dependencies are available."""
        result = check_azure_dependencies()
        
        # This should return True in test environment with mocked dependencies
        assert isinstance(result, bool)
    
    @pytest.mark.unit
    def test_get_azure_logging_config(self, mock_environment_variables):
        """Test get_azure_logging_config function."""
        config = get_azure_logging_config()
        
        assert isinstance(config, AzureLoggingConfig)
        assert config.environment == 'TEST'


@pytest.mark.azure
class TestAzureIntegration:
    """Azure integration test scenarios."""
    
    @pytest.mark.integration
    def test_azure_blob_storage_connection(self, mock_blob_service_client, mock_environment_variables):
        """Test Azure Blob Storage connection."""
        # This would test actual Azure Blob Storage connectivity
        pass
    
    @pytest.mark.integration
    def test_log_upload_to_azure(self, mock_blob_service_client, mock_environment_variables):
        """Test log upload to Azure Blob Storage."""
        # This would test actual log upload functionality
        pass


@pytest.mark.security
class TestSecurityScenarios:
    """Security-related test scenarios for logging."""
    
    @pytest.mark.unit
    def test_connection_string_security(self, mock_environment_variables):
        """Test secure handling of connection strings."""
        config = AzureLoggingConfig()
        
        # Test that connection strings are not logged inappropriately
        with patch('log_utils.config.logger') as mock_logger:
            config.log_configuration_status()
            
            # Verify that sensitive information is not logged
            for call in mock_logger.info.call_args_list:
                assert 'AccountKey=' not in str(call)
    
    @pytest.mark.unit
    def test_sensitive_data_filtering(self):
        """Test filtering of sensitive data in logs."""
        # Test that sensitive information is filtered from log messages
        pass


class TestErrorHandling:
    """Test cases for error handling scenarios."""
    
    @pytest.mark.unit
    @patch('log_utils.azure_sink.BlobServiceClient', side_effect=Exception("Connection failed"))
    def test_azure_sink_initialization_failure(self, mock_blob_client, mock_environment_variables):
        """Test Azure sink initialization failure."""
        with pytest.raises(Exception, match="Connection failed"):
            AzureBlobStorageSink(
                connection_string='test_connection',
                container_name='test-container',
                blob_prefix='test-prefix',
                environment='TEST'
            )
    
    @pytest.mark.unit
    @patch('log_utils.azure_sink.BlobServiceClient')
    def test_azure_sink_upload_failure_handling(self, mock_blob_client, mock_environment_variables):
        """Test handling of upload failures in Azure sink."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection',
            container_name='test-container',
            blob_prefix='test-prefix',
            environment='TEST'
        )
        
        # Mock upload failure
        mock_blob_client_instance = Mock()
        mock_blob_client_instance.get_blob_client.return_value.upload_blob.side_effect = Exception("Upload failed")
        sink.blob_service_client = mock_blob_client_instance
        sink.current_blob_name = 'test.log'
        sink.log_buffer = ['test log message']
        
        # Should handle upload failure gracefully
        sink._upload_buffer()
        
        # Buffer should be cleared even on failure to prevent memory issues
        assert len(sink.log_buffer) == 0
    
    @pytest.mark.unit
    def test_local_logging_fallback_on_permission_error(self, mock_environment_variables):
        """Test local logging fallback when file permissions are denied."""
        mock_config = Mock()
        mock_config.environment = 'TEST'
        
        with patch('log_utils.utils.logger.add', side_effect=PermissionError("Permission denied")):
            with pytest.raises(PermissionError):
                _setup_local_file_logging(False, mock_config)
