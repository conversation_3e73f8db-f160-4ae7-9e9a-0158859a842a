"""
Comprehensive unit tests for the Azure ML client module.

This module provides extensive test coverage for Azure ML integration functionality
including model downloading, configuration management, and error handling.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

import pytest
from azure.ai.ml import MLClient
from azure.ai.ml.entities import Model
from azure.core.exceptions import ResourceNotFoundError

# Import the module under test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / 'autolodge'))

from azure_ml_client import (
    AzureMLConfig,
    AzureMLModelDownloader,
    is_azure_ml_enabled,
    get_azure_ml_model_path,
    get_azure_ml_model_and_components,
    _calculate_md5_hash,
    _validate_model_integrity
)


class TestAzureMLConfig:
    """Test cases for AzureMLConfig dataclass."""
    
    @pytest.mark.unit
    def test_azure_ml_config_from_environment(self, mock_environment_variables):
        """Test AzureMLConfig creation from environment variables."""
        config = AzureMLConfig.from_environment()
        
        assert config.subscription_id == 'test-subscription-id'
        assert config.resource_group == 'test-resource-group'
        assert config.workspace_name == 'test-workspace'
        assert config.model_name == 'test-model'
    
    @pytest.mark.unit
    def test_azure_ml_config_missing_variables(self):
        """Test AzureMLConfig with missing environment variables."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Missing required Azure ML configuration"):
                AzureMLConfig.from_environment()
    
    @pytest.mark.unit
    def test_azure_ml_config_validation(self, mock_environment_variables):
        """Test AzureMLConfig validation."""
        config = AzureMLConfig.from_environment()
        
        # Test valid configuration
        assert config.subscription_id
        assert config.resource_group
        assert config.workspace_name
        assert config.model_name
    
    @pytest.mark.unit
    def test_azure_ml_config_optional_fields(self, mock_environment_variables):
        """Test AzureMLConfig optional fields."""
        config = AzureMLConfig.from_environment()
        
        # Test that optional fields have defaults
        assert config.model_version is None or isinstance(config.model_version, str)
        assert config.expected_md5_hash is None or isinstance(config.expected_md5_hash, str)
        assert isinstance(config.cache_dir, str)


class TestAzureMLModelDownloader:
    """Test cases for AzureMLModelDownloader class."""
    
    @pytest.mark.unit
    def test_downloader_initialization(self, mock_environment_variables):
        """Test AzureMLModelDownloader initialization."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        
        assert downloader.config == config
        assert downloader._ml_client is None
    
    @pytest.mark.unit
    @patch('azure_ml_client.MLClient')
    @patch('azure_ml_client.EnvironmentCredential')
    def test_ml_client_lazy_initialization(self, mock_credential, mock_ml_client, mock_environment_variables):
        """Test ML client lazy initialization."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        
        # Access ml_client property to trigger initialization
        client = downloader.ml_client
        
        mock_credential.assert_called_once()
        mock_ml_client.assert_called_once()
        assert client is not None
    
    @pytest.mark.unit
    @patch('azure_ml_client.MLClient')
    @patch('azure_ml_client.EnvironmentCredential', side_effect=Exception("Auth failed"))
    def test_ml_client_initialization_failure(self, mock_credential, mock_ml_client, mock_environment_variables):
        """Test ML client initialization failure."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        
        with pytest.raises(Exception, match="Auth failed"):
            _ = downloader.ml_client
    
    @pytest.mark.unit
    def test_get_latest_model_version(self, mock_azure_ml_client, mock_environment_variables):
        """Test getting latest model version."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        # Mock model list response
        mock_model_1 = Mock()
        mock_model_1.version = '1'
        mock_model_2 = Mock()
        mock_model_2.version = '2'
        mock_azure_ml_client.models.list.return_value = [mock_model_1, mock_model_2]
        
        version = downloader._get_latest_model_version()
        assert version == '2'
    
    @pytest.mark.unit
    def test_get_latest_model_version_no_models(self, mock_azure_ml_client, mock_environment_variables):
        """Test getting latest model version when no models exist."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        mock_azure_ml_client.models.list.return_value = []
        
        with pytest.raises(Exception, match="No models found"):
            downloader._get_latest_model_version()
    
    @pytest.mark.unit
    def test_download_model_success(self, mock_azure_ml_client, mock_environment_variables, temp_dir):
        """Test successful model download."""
        config = AzureMLConfig.from_environment()
        config.cache_dir = str(temp_dir)
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        # Create mock model file
        model_file = temp_dir / 'test_model.h5'
        model_file.write_text('mock model content')
        
        with patch.object(downloader, '_get_latest_model_version', return_value='1'), \
             patch('azure_ml_client.Path.glob', return_value=[model_file]):
            
            model_path, version = downloader._download_model()
            
            assert model_path == str(model_file)
            assert version == '1'
    
    @pytest.mark.unit
    def test_download_components_success(self, mock_azure_ml_client, mock_environment_variables, temp_dir):
        """Test successful components download."""
        config = AzureMLConfig.from_environment()
        config.cache_dir = str(temp_dir)
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        # Create mock component files
        components_dir = temp_dir / 'components'
        components_dir.mkdir()
        (components_dir / 'tk.pkl').write_text('tokenizer')
        (components_dir / 'le.pkl').write_text('label encoder')
        (components_dir / 'abbr.csv').write_text('abbreviations')
        (components_dir / 'corpus.pkl').write_text('corpus')
        
        with patch.object(downloader, '_get_latest_model_version', return_value='1'):
            components_path = downloader._download_components('1')
            
            assert Path(components_path).exists()
    
    @pytest.mark.unit
    def test_download_model_and_components_integration(self, mock_azure_ml_client, mock_environment_variables, temp_dir):
        """Test integrated model and components download."""
        config = AzureMLConfig.from_environment()
        config.cache_dir = str(temp_dir)
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        with patch.object(downloader, '_download_model', return_value=('model_path', '1')), \
             patch.object(downloader, '_download_components', return_value='components_path'):
            
            model_path, components_path, version = downloader.download_model_and_components()
            
            assert model_path == 'model_path'
            assert components_path == 'components_path'
            assert version == '1'


class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    @pytest.mark.unit
    def test_calculate_md5_hash(self, temp_dir):
        """Test MD5 hash calculation."""
        test_file = temp_dir / 'test_file.txt'
        test_content = b'test content for md5 calculation'
        test_file.write_bytes(test_content)
        
        hash_value = _calculate_md5_hash(str(test_file))
        
        # Verify hash is calculated correctly
        import hashlib
        expected_hash = hashlib.md5(test_content).hexdigest()
        assert hash_value == expected_hash
    
    @pytest.mark.unit
    def test_calculate_md5_hash_nonexistent_file(self):
        """Test MD5 hash calculation for nonexistent file."""
        with pytest.raises(FileNotFoundError):
            _calculate_md5_hash('nonexistent_file.txt')
    
    @pytest.mark.unit
    def test_validate_model_integrity_success(self, temp_dir):
        """Test successful model integrity validation."""
        test_file = temp_dir / 'test_model.h5'
        test_content = b'model content'
        test_file.write_bytes(test_content)
        
        import hashlib
        expected_hash = hashlib.md5(test_content).hexdigest()
        
        # Should not raise exception
        _validate_model_integrity(str(test_file), expected_hash)
    
    @pytest.mark.unit
    def test_validate_model_integrity_mismatch(self, temp_dir):
        """Test model integrity validation with hash mismatch."""
        test_file = temp_dir / 'test_model.h5'
        test_file.write_bytes(b'model content')
        
        wrong_hash = 'wrong_hash_value'
        
        with pytest.raises(ValueError, match="Model file integrity check failed"):
            _validate_model_integrity(str(test_file), wrong_hash)
    
    @pytest.mark.unit
    def test_validate_model_integrity_no_hash(self, temp_dir):
        """Test model integrity validation with no expected hash."""
        test_file = temp_dir / 'test_model.h5'
        test_file.write_bytes(b'model content')
        
        # Should not raise exception when no hash is provided
        _validate_model_integrity(str(test_file), None)
    
    @pytest.mark.unit
    def test_is_azure_ml_enabled_true(self, mock_environment_variables):
        """Test Azure ML enabled detection when enabled."""
        result = is_azure_ml_enabled()
        assert result is True
    
    @pytest.mark.unit
    def test_is_azure_ml_enabled_false(self):
        """Test Azure ML enabled detection when disabled."""
        with patch.dict(os.environ, {'AZURE_ML_ENABLE': 'false'}, clear=False):
            result = is_azure_ml_enabled()
            assert result is False
    
    @pytest.mark.unit
    def test_is_azure_ml_enabled_missing_config(self):
        """Test Azure ML enabled detection with missing configuration."""
        with patch.dict(os.environ, {}, clear=True):
            result = is_azure_ml_enabled()
            assert result is False


class TestHighLevelFunctions:
    """Test cases for high-level API functions."""
    
    @pytest.mark.unit
    @patch('azure_ml_client.AzureMLModelDownloader')
    @patch('azure_ml_client.AzureMLConfig.from_environment')
    def test_get_azure_ml_model_path(self, mock_config, mock_downloader, mock_environment_variables):
        """Test get_azure_ml_model_path function."""
        mock_config.return_value = Mock()
        mock_downloader_instance = Mock()
        mock_downloader.return_value = mock_downloader_instance
        mock_downloader_instance.download_model_and_components.return_value = ('model_path', 'components_path', '1')
        
        model_path, version = get_azure_ml_model_path()
        
        assert model_path == 'model_path'
        assert version == '1'
    
    @pytest.mark.unit
    @patch('azure_ml_client.AzureMLModelDownloader')
    @patch('azure_ml_client.AzureMLConfig.from_environment')
    def test_get_azure_ml_model_and_components(self, mock_config, mock_downloader, mock_environment_variables):
        """Test get_azure_ml_model_and_components function."""
        mock_config.return_value = Mock()
        mock_downloader_instance = Mock()
        mock_downloader.return_value = mock_downloader_instance
        mock_downloader_instance.download_model_and_components.return_value = ('model_path', 'components_path', '1')
        
        model_path, components_path, version = get_azure_ml_model_and_components()
        
        assert model_path == 'model_path'
        assert components_path == 'components_path'
        assert version == '1'


@pytest.mark.azure
class TestAzureIntegration:
    """Azure integration test scenarios."""
    
    @pytest.mark.integration
    def test_azure_ml_client_connection(self, mock_azure_credentials, mock_environment_variables):
        """Test Azure ML client connection."""
        # This would test actual Azure ML connectivity in integration environment
        pass
    
    @pytest.mark.integration
    def test_model_registry_access(self, mock_azure_ml_client, mock_environment_variables):
        """Test model registry access."""
        # This would test actual model registry operations
        pass
    
    @pytest.mark.integration
    def test_authentication_scenarios(self, mock_environment_variables):
        """Test various authentication scenarios."""
        # Test different authentication methods
        pass


@pytest.mark.security
class TestSecurityScenarios:
    """Security-related test scenarios for Azure ML client."""
    
    @pytest.mark.unit
    def test_credential_handling(self, mock_environment_variables):
        """Test secure credential handling."""
        config = AzureMLConfig.from_environment()
        
        # Ensure credentials are not logged or exposed
        assert config.subscription_id
        # Test that sensitive data is handled securely
    
    @pytest.mark.unit
    def test_model_integrity_validation(self, temp_dir):
        """Test model file integrity validation."""
        # Test that model files are validated for integrity
        test_file = temp_dir / 'test_model.h5'
        test_file.write_bytes(b'model content')
        
        # Test with correct hash
        import hashlib
        correct_hash = hashlib.md5(b'model content').hexdigest()
        _validate_model_integrity(str(test_file), correct_hash)
        
        # Test with incorrect hash
        with pytest.raises(ValueError):
            _validate_model_integrity(str(test_file), 'wrong_hash')


class TestErrorHandling:
    """Test cases for error handling scenarios."""
    
    @pytest.mark.unit
    def test_network_error_handling(self, mock_azure_ml_client, mock_environment_variables):
        """Test handling of network errors."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        # Mock network error
        mock_azure_ml_client.models.download.side_effect = Exception("Network error")
        
        with pytest.raises(Exception, match="Network error"):
            downloader._download_model()
    
    @pytest.mark.unit
    def test_resource_not_found_handling(self, mock_azure_ml_client, mock_environment_variables):
        """Test handling of resource not found errors."""
        config = AzureMLConfig.from_environment()
        downloader = AzureMLModelDownloader(config)
        downloader._ml_client = mock_azure_ml_client
        
        # Mock resource not found error
        mock_azure_ml_client.models.get.side_effect = ResourceNotFoundError("Model not found")
        
        with pytest.raises(ResourceNotFoundError):
            downloader._get_latest_model_version()
    
    @pytest.mark.unit
    def test_configuration_error_handling(self):
        """Test handling of configuration errors."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Missing required Azure ML configuration"):
                AzureMLConfig.from_environment()
