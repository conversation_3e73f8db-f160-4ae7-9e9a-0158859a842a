"""
Comprehensive unit tests for the scoring module.

This module provides extensive test coverage for the autolodge scoring functionality
including model loading, preprocessing, inference, and error handling scenarios.
"""

import json
import os

# Import the module under test
import sys
from pathlib import Path
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
import pytest

sys.path.insert(0, str(Path(__file__).parent.parent / 'autolodge'))

from score import (
    _comprehensive_input_logging,
    _log_input_metadata,
    _log_input_sample,
    _validate_required_fields,
    init,
    run,
)


class TestScoringConfig:
    """Test cases for ScoringConfig dataclass."""

    @pytest.mark.unit
    def test_scoring_config_initialization(self, mock_environment_variables):
        """Test ScoringConfig initialization with environment variables."""
        config = ScoringConfig()

        assert config.MODEL_FILE_PATH == 'test_model.h5'
        assert config.LOG_INPUT_LEVEL == 'METADATA_ONLY'
        assert config.REQUIRED_UTILS == ['le.pkl', 'tk.pkl', 'abbr.csv', 'corpus.pkl']

    @pytest.mark.unit
    def test_scoring_config_defaults(self):
        """Test ScoringConfig default values."""
        with patch.dict(os.environ, {}, clear=True):
            config = ScoringConfig()

            assert config.MODEL_FILE_PATH == 'resources/autolodge_20250605.h5'
            assert config.LOG_INPUT_LEVEL == 'METADATA_ONLY'
            assert len(config.REQUIRED_UTILS) == 4

    @pytest.mark.unit
    def test_scoring_config_environment_override(self):
        """Test ScoringConfig environment variable override."""
        test_env = {'MODEL_FILE_PATH': 'custom_model.h5', 'AUTOLODGE_LOG_INPUT_LEVEL': 'FULL'}

        with patch.dict(os.environ, test_env, clear=False):
            config = ScoringConfig()

            assert config.MODEL_FILE_PATH == 'custom_model.h5'
            assert config.LOG_INPUT_LEVEL == 'FULL'


class TestResourceManager:
    """Test cases for ResourceManager class."""

    @pytest.mark.unit
    def test_resource_manager_initialization(self, mock_environment_variables):
        """Test ResourceManager initialization."""
        config = ScoringConfig()
        manager = ResourceManager(config)

        assert manager.config == config
        assert not manager._initialized
        assert manager.model is None
        assert manager.tokenizer is None
        assert manager.label_encoder is None

    @pytest.mark.unit
    @patch('score.AZURE_ML_AVAILABLE', True)
    @patch('score.is_azure_ml_enabled', return_value=True)
    def test_resource_manager_azure_mode_detection(self, mock_azure_enabled, mock_environment_variables):
        """Test ResourceManager Azure ML mode detection."""
        config = ScoringConfig()
        manager = ResourceManager(config)

        with patch.object(manager, '_initialize_azure_ml_mode') as mock_azure_init:
            manager.initialize()
            mock_azure_init.assert_called_once()

    @pytest.mark.unit
    @patch('score.AZURE_ML_AVAILABLE', False)
    def test_resource_manager_local_mode_detection(self, mock_environment_variables):
        """Test ResourceManager local mode detection."""
        config = ScoringConfig()
        manager = ResourceManager(config)

        with patch.object(manager, '_initialize_local_mode') as mock_local_init:
            manager.initialize()
            mock_local_init.assert_called_once()

    @pytest.mark.unit
    def test_resource_manager_fallback_mechanism(self, mock_environment_variables):
        """Test ResourceManager fallback from Azure to local mode."""
        config = ScoringConfig()
        manager = ResourceManager(config)

        with (
            patch('score.AZURE_ML_AVAILABLE', True),
            patch('score.is_azure_ml_enabled', return_value=True),
            patch.object(manager, '_initialize_azure_ml_mode', side_effect=Exception('Azure failed')),
            patch.object(manager, '_initialize_local_mode') as mock_local_init,
        ):
            manager.initialize()
            mock_local_init.assert_called_once()

    @pytest.mark.unit
    def test_resource_manager_double_initialization_prevention(self, mock_environment_variables):
        """Test ResourceManager prevents double initialization."""
        config = ScoringConfig()
        manager = ResourceManager(config)
        manager._initialized = True

        with (
            patch.object(manager, '_initialize_azure_ml_mode') as mock_azure_init,
            patch.object(manager, '_initialize_local_mode') as mock_local_init,
        ):
            manager.initialize()
            mock_azure_init.assert_not_called()
            mock_local_init.assert_not_called()


class TestInputValidation:
    """Test cases for input validation functions."""

    @pytest.mark.unit
    def test_validate_required_fields_valid_input(self, sample_dataframe):
        """Test validation with valid input data."""
        result = _validate_required_fields(sample_dataframe)

        assert result['valid'] is True
        assert result['total_records'] == 3
        assert len(result['missing_fields']) == 0
        assert len(result['invalid_records']) == 0

    @pytest.mark.unit
    def test_validate_required_fields_missing_columns(self):
        """Test validation with missing required columns."""
        invalid_df = pd.DataFrame({'invalid_col': [1, 2, 3]})
        result = _validate_required_fields(invalid_df)

        assert result['valid'] is False
        assert 'Pair_ID' in result['missing_fields']
        assert 'T' in result['missing_fields']

    @pytest.mark.unit
    def test_validate_required_fields_empty_dataframe(self):
        """Test validation with empty DataFrame."""
        empty_df = pd.DataFrame()
        result = _validate_required_fields(empty_df)

        assert result['valid'] is False
        assert result['total_records'] == 0

    @pytest.mark.unit
    def test_validate_required_fields_null_values(self):
        """Test validation with null values in required fields."""
        df_with_nulls = pd.DataFrame({
            'Pair_ID': [1, None, 3],
            'T': ['text1', 'text2', None],
            'AmountExVat': [10.0, 20.0, 30.0],
        })
        result = _validate_required_fields(df_with_nulls)

        assert result['valid'] is False
        assert len(result['invalid_records']) > 0


class TestInputLogging:
    """Test cases for input logging functions."""

    @pytest.mark.unit
    def test_log_input_metadata(self, sample_dataframe, caplog):
        """Test input metadata logging."""
        with caplog.at_level('INFO'):
            _log_input_metadata(sample_dataframe)

        assert '📊 Input data statistics' in caplog.text
        assert 'Total records: 3' in caplog.text

    @pytest.mark.unit
    def test_log_input_full(self, sample_dataframe, caplog):
        """Test full input data logging."""
        with caplog.at_level('DEBUG'):
            _log_input_full(sample_dataframe)

        assert 'Complete input data' in caplog.text

    @pytest.mark.unit
    def test_log_input_sample(self, sample_dataframe, caplog):
        """Test sample input data logging."""
        with caplog.at_level('DEBUG'):
            _log_input_sample(sample_dataframe, sample_size=2)

        assert 'Sample input data' in caplog.text

    @pytest.mark.unit
    def test_comprehensive_input_logging_full_mode(self, sample_dataframe):
        """Test comprehensive input logging in FULL mode."""
        with (
            patch('score._log_input_full') as mock_full,
            patch('score._validate_required_fields', return_value={'valid': True}) as mock_validate,
            patch('score._log_input_metadata') as mock_metadata,
        ):
            result = _comprehensive_input_logging(sample_dataframe, 'FULL')

            mock_validate.assert_called_once()
            mock_metadata.assert_called_once()
            mock_full.assert_called_once()

    @pytest.mark.unit
    def test_comprehensive_input_logging_sample_mode(self, sample_dataframe):
        """Test comprehensive input logging in SAMPLE mode."""
        with (
            patch('score._log_input_sample') as mock_sample,
            patch('score._validate_required_fields', return_value={'valid': True}) as mock_validate,
            patch('score._log_input_metadata') as mock_metadata,
        ):
            result = _comprehensive_input_logging(sample_dataframe, 'SAMPLE')

            mock_validate.assert_called_once()
            mock_metadata.assert_called_once()
            mock_sample.assert_called_once()

    @pytest.mark.unit
    def test_comprehensive_input_logging_metadata_only_mode(self, sample_dataframe):
        """Test comprehensive input logging in METADATA_ONLY mode."""
        with (
            patch('score._validate_required_fields', return_value={'valid': True}) as mock_validate,
            patch('score._log_input_metadata') as mock_metadata,
        ):
            result = _comprehensive_input_logging(sample_dataframe, 'METADATA_ONLY')

            mock_validate.assert_called_once()
            mock_metadata.assert_called_once()


class TestScoringFunctions:
    """Test cases for main scoring functions."""

    @pytest.mark.unit
    def test_init_function(self, mock_environment_variables):
        """Test init function."""
        with patch('score._get_resource_manager') as mock_get_manager:
            mock_manager = Mock()
            mock_get_manager.return_value = mock_manager

            init()

            mock_get_manager.assert_called_once()
            mock_manager.initialize.assert_called_once()

    @pytest.mark.unit
    def test_run_function_valid_input(self, sample_input_data, mock_environment_variables):
        """Test run function with valid input."""
        mock_manager = Mock()
        mock_manager._initialized = True
        mock_manager.model = Mock()
        mock_manager.tokenizer = Mock()
        mock_manager.label_encoder = Mock()
        mock_manager.abbreviations_dict = {}
        mock_manager.corpus = set()

        # Mock prediction results
        mock_manager.model.predict.return_value = np.array([[0.1, 0.9], [0.8, 0.2], [0.3, 0.7]])
        mock_manager.tokenizer.texts_to_sequences.return_value = [[1, 2], [3, 4], [5, 6]]
        mock_manager.label_encoder.inverse_transform.return_value = ['Category1', 'Category2', 'Category3']
        mock_manager.label_encoder.classes_ = ['Category1', 'Category2']

        with (
            patch('score._get_resource_manager', return_value=mock_manager),
            patch(
                'tensorflow.keras.preprocessing.sequence.pad_sequences', return_value=np.array([[1, 2], [3, 4], [5, 6]])
            ),
        ):
            result = run(sample_input_data)

            assert 'predictions' in result
            assert 'api_version' in result
            assert len(result['predictions']) == 3

    @pytest.mark.unit
    def test_run_function_invalid_json(self, mock_environment_variables):
        """Test run function with invalid JSON input."""
        invalid_json = 'invalid json string'

        with pytest.raises(ValueError, match='Invalid JSON format'):
            run(invalid_json)

    @pytest.mark.unit
    def test_run_function_uninitialized_model(self, sample_input_data, mock_environment_variables):
        """Test run function with uninitialized model."""
        mock_manager = Mock()
        mock_manager._initialized = False

        with patch('score._get_resource_manager', return_value=mock_manager):
            with pytest.raises(RuntimeError, match='Model components are not initialized'):
                run(sample_input_data)


class TestErrorHandling:
    """Test cases for error handling scenarios."""

    @pytest.mark.unit
    def test_azure_ml_import_error_handling(self):
        """Test handling of Azure ML import errors."""
        with patch('score.logger') as mock_logger:
            # This would be tested by importing the module with mocked imports
            # For now, we test that the AZURE_ML_AVAILABLE flag works correctly
            assert hasattr(sys.modules.get('score', Mock()), 'AZURE_ML_AVAILABLE')

    @pytest.mark.unit
    def test_model_loading_error_handling(self, mock_environment_variables):
        """Test error handling during model loading."""
        config = ScoringConfig()
        manager = ResourceManager(config)

        with (
            patch('score.AZURE_ML_AVAILABLE', False),
            patch.object(manager, '_initialize_local_mode', side_effect=Exception('Model loading failed')),
        ):
            with pytest.raises(Exception, match='Model loading failed'):
                manager.initialize()

    @pytest.mark.unit
    def test_prediction_error_handling(self, sample_input_data, mock_environment_variables):
        """Test error handling during prediction."""
        mock_manager = Mock()
        mock_manager._initialized = True
        mock_manager.model.predict.side_effect = Exception('Prediction failed')

        with patch('score._get_resource_manager', return_value=mock_manager):
            with pytest.raises(Exception, match='Inference failed'):
                run(sample_input_data)


@pytest.mark.integration
class TestIntegrationScenarios:
    """Integration test scenarios."""

    def test_end_to_end_local_mode(self, test_resources_dir, mock_environment_variables):
        """Test end-to-end scoring in local mode."""
        # This would require more complex setup with actual model files
        # For now, we test the integration points
        pass

    def test_end_to_end_azure_mode(self, mock_azure_ml_client, mock_environment_variables):
        """Test end-to-end scoring in Azure ML mode."""
        # This would test the full Azure ML integration
        pass


@pytest.mark.security
class TestSecurityScenarios:
    """Security-related test scenarios."""

    @pytest.mark.unit
    def test_input_sanitization(self):
        """Test input data sanitization."""
        malicious_input = json.dumps([
            {'Pair_ID': '<script>alert("xss")</script>', 'T': 'SELECT * FROM users', 'AmountExVat': 100.0}
        ])

        # Test that malicious input is handled safely
        # This would depend on actual sanitization implementation
        pass

    @pytest.mark.unit
    def test_sensitive_data_logging(self, sample_input_data):
        """Test that sensitive data is not logged inappropriately."""
        with patch('score.logger') as mock_logger:
            # Test that sensitive information is not logged in production
            pass
