"""
Basic functionality tests for the autolodge system.

This module provides basic test coverage to ensure the system works correctly.
"""

import os
import sys
from pathlib import Path
from unittest.mock import patch

import pytest

# Add autolodge to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'autolodge'))


class TestBasicFunctionality:
    """Test basic functionality of the autolodge system."""

    @pytest.mark.unit
    def test_imports_work(self):
        """Test that basic imports work."""
        try:
            import azure_ml_client
            import log_utils
            import score

            assert True
        except ImportError as e:
            pytest.fail(f'Import failed: {e}')

    @pytest.mark.unit
    def test_config_creation(self):
        """Test that Config can be created."""
        try:
            import score

            config = score.Config()
            assert config.BATCH_SIZE == 8
            assert config.TOP_K_PREDICTIONS == 5
            assert config.API_VERSION == 7
        except Exception as e:
            pytest.fail(f'Config creation failed: {e}')

    @pytest.mark.unit
    def test_validate_required_fields_empty_data(self):
        """Test validation with empty data."""
        try:
            import score

            result = score._validate_required_fields([])
            assert result['valid'] is False
            assert result['total_records'] == 0
        except Exception as e:
            pytest.fail(f'Validation test failed: {e}')

    @pytest.mark.unit
    def test_validate_required_fields_valid_data(self):
        """Test validation with valid data."""
        try:
            import score

            test_data = [
                {'Pair_ID': 1, 'T': 'Test treatment', 'AmountExVat': 100.0},
                {'Pair_ID': 2, 'T': 'Another treatment', 'AmountExVat': 200.0},
            ]
            result = score._validate_required_fields(test_data)
            assert result['valid'] is True
            assert result['total_records'] == 2
        except Exception as e:
            pytest.fail(f'Validation test failed: {e}')

    @pytest.mark.unit
    def test_log_input_metadata(self):
        """Test input metadata logging."""
        try:
            import score

            test_data = [{'Pair_ID': 1, 'T': 'Test treatment', 'AmountExVat': 100.0}]
            # This should not raise an exception
            score._log_input_metadata(test_data)
            assert True
        except Exception as e:
            pytest.fail(f'Metadata logging test failed: {e}')

    @pytest.mark.unit
    def test_comprehensive_input_logging(self):
        """Test comprehensive input logging."""
        try:
            import score

            test_data = [{'Pair_ID': 1, 'T': 'Test treatment', 'AmountExVat': 100.0}]
            result = score._comprehensive_input_logging(test_data, 'METADATA_ONLY')
            assert 'valid' in result
            assert 'total_records' in result
        except Exception as e:
            pytest.fail(f'Comprehensive logging test failed: {e}')


class TestAzureMLClient:
    """Test Azure ML client functionality."""

    @pytest.mark.unit
    def test_azure_ml_config_creation(self):
        """Test Azure ML config creation."""
        try:
            import azure_ml_client

            # This should work with environment variables
            with patch.dict(
                os.environ,
                {
                    'AZURE_ML_SUBSCRIPTION_ID': 'test-sub',
                    'AZURE_ML_RESOURCE_GROUP': 'test-rg',
                    'AZURE_ML_WORKSPACE_NAME': 'test-ws',
                    'AZURE_ML_MODEL_NAME': 'test-model',
                },
            ):
                config = azure_ml_client.AzureMLConfig.from_environment()
                assert config.subscription_id == 'test-sub'
                assert config.resource_group == 'test-rg'
                assert config.workspace_name == 'test-ws'
                assert config.model_name == 'test-model'
        except Exception as e:
            pytest.fail(f'Azure ML config test failed: {e}')

    @pytest.mark.unit
    def test_is_azure_ml_enabled(self):
        """Test Azure ML enabled detection."""
        try:
            import azure_ml_client

            # Test with enabled
            with patch.dict(os.environ, {'AZURE_ML_ENABLE': 'true'}):
                assert azure_ml_client.is_azure_ml_enabled() is True

            # Test with disabled
            with patch.dict(os.environ, {'AZURE_ML_ENABLE': 'false'}):
                assert azure_ml_client.is_azure_ml_enabled() is False
        except Exception as e:
            pytest.fail(f'Azure ML enabled test failed: {e}')


class TestLogUtils:
    """Test logging utilities."""

    @pytest.mark.unit
    def test_azure_logging_config_creation(self):
        """Test Azure logging config creation."""
        try:
            import log_utils

            config = log_utils.AzureLoggingConfig()
            assert hasattr(config, 'container_name')
            assert hasattr(config, 'blob_prefix')
            assert hasattr(config, 'environment')
        except Exception as e:
            pytest.fail(f'Azure logging config test failed: {e}')

    @pytest.mark.unit
    def test_setup_logging_function_exists(self):
        """Test that setup_logging function exists and can be called."""
        try:
            import log_utils

            # This should not raise an exception
            result = log_utils.setup_logging()
            assert isinstance(result, str)
        except Exception as e:
            pytest.fail(f'Setup logging test failed: {e}')


class TestDeployment:
    """Test deployment functionality."""

    @pytest.mark.unit
    def test_deployment_config_creation(self):
        """Test deployment config creation."""
        try:
            sys.path.insert(0, str(Path(__file__).parent.parent / 'deploy'))
            import submit_deploy_v2

            config = submit_deploy_v2.DeploymentConfig(
                subscription_id='test-sub',
                resource_group='test-rg',
                workspace_name='test-ws',
                model_name='test-model',
                env_name='test-env',
                endpoint_name='test-endpoint',
                deployment_name='test-deployment',
                model_file_path='test_model.h5',
                conda_file_path='test_env.yaml',
                deployment_pythonpath='/test/path',
                model_file_md5_hash='test-hash',
            )

            assert config.subscription_id == 'test-sub'
            assert config.resource_group == 'test-rg'
            assert config.workspace_name == 'test-ws'
        except Exception as e:
            pytest.fail(f'Deployment config test failed: {e}')


@pytest.mark.integration
class TestIntegrationBasics:
    """Basic integration tests."""

    def test_full_import_chain(self):
        """Test that all modules can be imported together."""
        try:
            sys.path.insert(0, str(Path(__file__).parent.parent / 'deploy'))
            assert True
        except Exception as e:
            pytest.fail(f'Full import chain test failed: {e}')


if __name__ == '__main__':
    pytest.main([__file__])
