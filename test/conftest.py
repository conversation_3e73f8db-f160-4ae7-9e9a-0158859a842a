"""
Test configuration and fixtures for autolodge_retrained_deploy.

This module provides pytest fixtures and configuration for comprehensive testing
of the autolodge deployment system with proper mocking for Azure services.
"""

import os
import tempfile
from pathlib import Path
from typing import Dict, Any, Generator
from unittest.mock import Mock, MagicMock, patch

import pytest
import pandas as pd
import numpy as np
from azure.ai.ml import MLClient
from azure.ai.ml.entities import Model, Environment, ManagedOnlineEndpoint, ManagedOnlineDeployment
from azure.storage.blob import BlobServiceClient

# Test data constants
TEST_MODEL_DATA = {
    'Pair_ID': [1001, 1002, 1003],
    'T': ['Small Animal Consultation', 'Cytology(SVC)', 'Discount'],
    'AmountExVat': [80.91, 63.64, -69.0]
}

TEST_PREDICTIONS = [
    {'Pair_ID': 1001, 'predicted_category': 'Consultation', 'confidence': 0.95},
    {'Pair_ID': 1002, 'predicted_category': 'Diagnostic', 'confidence': 0.87},
    {'Pair_ID': 1003, 'predicted_category': 'Financial', 'confidence': 0.92}
]


@pytest.fixture(scope="session")
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_environment_variables() -> Generator[Dict[str, str], None, None]:
    """Mock environment variables for testing."""
    env_vars = {
        'AZURE_SUBSCRIPTION_ID': 'test-subscription-id',
        'AZURE_RESOURCE_GROUP': 'test-resource-group',
        'AZURE_ML_WORKSPACE_NAME': 'test-workspace',
        'AZURE_TENANT_ID': 'test-tenant-id',
        'AZURE_CLIENT_ID': 'test-client-id',
        'AZURE_CLIENT_SECRET': 'test-client-secret',
        'MODEL_NAME': 'test-model',
        'ENV_NAME': 'test-env',
        'ENDPOINT_NAME': 'test-endpoint',
        'DEPLOYMENT_NAME': 'test-deployment',
        'AZURE_ML_MODEL_NAME': 'test-model',
        'AZURE_ML_ENABLE': 'true',
        'AZURE_STORAGE_CONNECTION_STRING': 'DefaultEndpointsProtocol=https;AccountName=test;AccountKey=test;EndpointSuffix=core.windows.net',
        'AZURE_LOG_CONTAINER_NAME': 'test-logs',
        'AZURE_LOG_BLOB_PREFIX': 'test',
        'DEPLOYMENT_ENVIRONMENT': 'TEST',
        'AUTOLODGE_LOG_INPUT_LEVEL': 'METADATA_ONLY',
        'MODEL_FILE_PATH': 'test_model.h5',
        'MODEL_FILE_MD5_HASH': 'test-md5-hash'
    }
    
    with patch.dict(os.environ, env_vars, clear=False):
        yield env_vars


@pytest.fixture
def mock_azure_ml_client() -> Mock:
    """Mock Azure ML client for testing."""
    mock_client = Mock(spec=MLClient)
    
    # Mock model operations
    mock_model = Mock(spec=Model)
    mock_model.name = 'test-model'
    mock_model.version = '1'
    mock_model.path = 'test_model.h5'
    
    mock_client.models.create_or_update.return_value = mock_model
    mock_client.models.get.return_value = mock_model
    mock_client.models.download.return_value = None
    
    # Mock environment operations
    mock_environment = Mock(spec=Environment)
    mock_environment.name = 'test-env'
    mock_environment.version = '1'
    
    mock_client.environments.create_or_update.return_value = mock_environment
    mock_client.environments.get.return_value = mock_environment
    
    # Mock endpoint operations
    mock_endpoint = Mock(spec=ManagedOnlineEndpoint)
    mock_endpoint.name = 'test-endpoint'
    mock_endpoint.traffic = {'test-deployment': 100}
    
    mock_client.online_endpoints.create_or_update.return_value = mock_endpoint
    mock_client.online_endpoints.get.return_value = mock_endpoint
    mock_client.online_endpoints.begin_create_or_update.return_value = Mock()
    
    # Mock deployment operations
    mock_deployment = Mock(spec=ManagedOnlineDeployment)
    mock_deployment.name = 'test-deployment'
    mock_deployment.provisioning_state = 'Succeeded'
    
    mock_client.online_deployments.create_or_update.return_value = mock_deployment
    mock_client.online_deployments.get.return_value = mock_deployment
    mock_client.online_deployments.begin_create_or_update.return_value = Mock()
    
    return mock_client


@pytest.fixture
def mock_blob_service_client() -> Mock:
    """Mock Azure Blob Storage client for testing."""
    mock_client = Mock(spec=BlobServiceClient)
    mock_blob_client = Mock()
    mock_blob_client.upload_blob.return_value = None
    mock_client.get_blob_client.return_value = mock_blob_client
    return mock_client


@pytest.fixture
def sample_input_data() -> str:
    """Sample input data for testing scoring functionality."""
    import json
    return json.dumps(TEST_MODEL_DATA)


@pytest.fixture
def sample_dataframe() -> pd.DataFrame:
    """Sample DataFrame for testing."""
    return pd.DataFrame(TEST_MODEL_DATA)


@pytest.fixture
def mock_keras_model() -> Mock:
    """Mock Keras model for testing."""
    mock_model = Mock()
    mock_model.predict.return_value = np.array([[0.1, 0.9], [0.8, 0.2], [0.3, 0.7]])
    return mock_model


@pytest.fixture
def mock_tokenizer() -> Mock:
    """Mock tokenizer for testing."""
    mock_tokenizer = Mock()
    mock_tokenizer.texts_to_sequences.return_value = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
    return mock_tokenizer


@pytest.fixture
def mock_label_encoder() -> Mock:
    """Mock label encoder for testing."""
    mock_encoder = Mock()
    mock_encoder.inverse_transform.return_value = ['Consultation', 'Diagnostic', 'Financial']
    mock_encoder.classes_ = ['Consultation', 'Diagnostic', 'Financial']
    return mock_encoder


@pytest.fixture
def test_resources_dir(temp_dir: Path) -> Path:
    """Create test resources directory with mock files."""
    resources_dir = temp_dir / 'resources'
    resources_dir.mkdir(exist_ok=True)
    
    # Create mock files
    (resources_dir / 'autolodge_20250605.h5').touch()
    (resources_dir / 'tk.pkl').touch()
    (resources_dir / 'le.pkl').touch()
    (resources_dir / 'corpus.pkl').touch()
    
    # Create mock abbreviations CSV
    abbr_df = pd.DataFrame({
        'abbreviation': ['vet', 'exam'],
        'expansion': ['veterinary', 'examination']
    })
    abbr_df.to_csv(resources_dir / 'abbr.csv', index=False)
    
    return resources_dir


@pytest.fixture
def mock_file_operations():
    """Mock file operations for testing."""
    with patch('pathlib.Path.exists', return_value=True), \
         patch('pathlib.Path.stat') as mock_stat, \
         patch('builtins.open', create=True) as mock_open:
        
        mock_stat.return_value.st_size = 1024
        mock_open.return_value.__enter__.return_value.read.return_value = b'mock file content'
        yield


@pytest.fixture(autouse=True)
def reset_loguru():
    """Reset loguru configuration between tests."""
    from loguru import logger
    logger.remove()
    logger.add(lambda msg: None)  # Silent handler for tests
    yield
    logger.remove()


@pytest.fixture
def mock_azure_credentials():
    """Mock Azure credentials for testing."""
    with patch('azure.identity.EnvironmentCredential') as mock_cred:
        mock_cred.return_value = Mock()
        yield mock_cred


# Test categories for pytest markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "azure: Azure integration tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "security: Security-related tests")
